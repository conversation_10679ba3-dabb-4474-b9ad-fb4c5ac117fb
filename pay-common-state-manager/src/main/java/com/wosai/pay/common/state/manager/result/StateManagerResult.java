package com.wosai.pay.common.state.manager.result;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class StateManagerResult {

    /**
     * 状态值 false true , not null
     */
    private Boolean state;

    /**
     * 子状态列表
     */
    private List<SubState> subStateList;

    @Data
    public static class SubState {
        /**
         * 子状态类型
         */
        private Integer type;
        /**
         * 子状态描述
         */
        private String desc;

        /**
         * 子状态布尔值
         */
        private Boolean value;

        /**
         * 操作备注
         */
        private String remark;

    }
}
