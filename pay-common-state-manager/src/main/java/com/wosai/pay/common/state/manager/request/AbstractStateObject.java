package com.wosai.pay.common.state.manager.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import org.reflections.Reflections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;


@Data
public abstract class AbstractStateObject {

    public static final Logger logger = LoggerFactory.getLogger(AbstractStateObject.class);

    private static final Map<String, Class<? extends AbstractStateObject>> typeId2Class = new HashMap<>();
    private static volatile boolean initialized = false;

    public static void registerType(String objectType, Class<? extends AbstractStateObject> clazz) {
        typeId2Class.put(String.format("%s", objectType), clazz);
    }

    // 添加自定义反序列化器
    public static class AbstractStateObjectDeserializer
            extends JsonDeserializer<AbstractStateObject> {

        @Override
        public AbstractStateObject deserialize(
                JsonParser parser,
                DeserializationContext context
        ) throws IOException {

            // 解析为JSON树节点
            JsonNode node = parser.getCodec().readTree(parser);

            // 验证必须包含objectType字段
            if (!node.has("objectType")) {
                throw new IllegalStateException(
                        "JSON数据缺少必需的objectType字段");
            }

            String objectType = node.get("objectType").asText();

            // 获取目标类
            Class<? extends AbstractStateObject> targetClass = AbstractStateObject.classFor(objectType);
            if (targetClass == null) {
                throw new IllegalArgumentException(
                        "未知的对象类型: " + objectType);
            }

            // 转换为具体类型
            return parser.getCodec().treeToValue(node, targetClass);
        }
    }

    public static Class<? extends AbstractStateObject> classFor(String objectType) {
        if (!initialized) {
            synchronized (AbstractStateObject.class) {
                if (!initialized) {
                    initialize();
                    initialized = true;
                }
            }
        }
        return typeId2Class.get(String.format("%s", objectType));
    }

    private static void initialize() {
        Reflections reflections = new Reflections(AbstractStateObject.class.getPackage().getName());
        Set<Class<? extends AbstractStateObject>> subTypes = reflections.getSubTypesOf(AbstractStateObject.class);
        for (Class<? extends AbstractStateObject> type : subTypes) {
            if (!Modifier.isAbstract(type.getModifiers())) {
                try {
                    type.newInstance();
                } catch (InstantiationException | IllegalAccessException e) {
                    logger.error("failed to load event type {}", type.getName(), e);
                }
            }
        }
    }



    private String objectType;



    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }
}
