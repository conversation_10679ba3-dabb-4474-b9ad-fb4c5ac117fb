package com.wosai.pay.common.state.manager.dao;

import com.wosai.pay.common.data.Jackson2PersistenceHelper;
import com.wosai.pay.common.data.jdbc.JdbcVersionedRecordDao;


import com.wosai.pay.common.state.manager.constant.StateManagerConstant;
import com.wosai.pay.common.state.manager.entity.AbstractStateDO;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

public class StateDao extends JdbcVersionedRecordDao<Long, AbstractStateDO> {

    private static final String TABLE_NAME = StateManagerConstant.TABLE_STATE;

    public StateDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(TABLE_NAME, AbstractStateDO.class, "", namedParameterJdbcTemplate, new Jackson2PersistenceHelper());
    }

    public StateDao(String tableName, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(tableName, AbstractStateDO.class, "", namedParameterJdbcTemplate, new Jackson2PersistenceHelper());
    }
}

