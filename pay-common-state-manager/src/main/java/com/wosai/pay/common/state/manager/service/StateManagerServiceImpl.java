package com.wosai.pay.common.state.manager.service;

import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.state.manager.constant.StateManagerConstant;
import com.wosai.pay.common.state.manager.dao.StateDao;
import com.wosai.pay.common.state.manager.request.AbstractEntity;
import com.wosai.pay.common.state.manager.request.StateManagerRequest;
import com.wosai.pay.common.state.manager.entity.AbstractStateDO;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import com.wosai.pay.common.state.manager.exception.StateManageBizException;
import com.wosai.pay.common.state.manager.result.StateConfigurationResult;
import com.wosai.pay.common.state.manager.result.StateManagerResult;
import com.wosai.pay.common.state.manager.registry.StateManagerProcessor;
import com.wosai.pay.common.state.manager.registry.StateManagerProcessorRegistry;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 状态服务实现类
 * 实现了状态的查询和更新逻辑，支持不同实体类型的自定义处理
 */
public class StateManagerServiceImpl implements StateManagerService {

    private static final Logger LOGGER = Logger.getLogger(StateManagerServiceImpl.class.getName());

    private final StateDao stateDao;

    public StateManagerServiceImpl(StateDao stateDao) {
        this.stateDao = stateDao;
    }

    /**
     * 注册一个状态处理器
     *
     * @param processor 要注册的处理器
     * @return 注册是否成功
     */
    public boolean registerProcessor(StateManagerProcessor<?, ?> processor) {
        return StateManagerProcessorRegistry.register(processor);
    }


    @Override
    @SuppressWarnings("unchecked")
    public <T extends AbstractEntity, R extends StateManagerResult> R queryState(StateManagerRequest<T> stateManagerRequest) {
        String business = stateManagerRequest.getBusiness();
        String entityType = stateManagerRequest.getEntityType();
        StateManagerConfig.validBizAndType(business, null);
        List<StateConfigurationResult.SubStateConfiguration> stateConfigurationList = StateManagerConfig.getStatesList(business);

        // 获取实体类型对应的处理器
        StateManagerProcessor<AbstractEntity, StateManagerResult> processor = StateManagerProcessorRegistry.getProcessor(entityType);
        if (processor == null) {
            throw new IllegalStateException("未找到实体类型[" + entityType + "]对应的处理器");
        }

        T entity = stateManagerRequest.getEntity();

        //初始化数据库DO
        AbstractStateDO abstractStateDO = initAndGetStateByBizIdAndEntity(business, entity, processor);

        // 创建返回结果
        R result = (R) processor.createResultInstance(entity);

        // 设置基本状态信息
        result.setState(abstractStateDO.getState());

        // 设置子状态列表
        List<StateManagerResult.SubState> subStateList = new ArrayList<>();
        Map<Integer, Boolean> subStates = abstractStateDO.getSubStates();

        if (stateConfigurationList != null) {
            for (StateConfigurationResult.SubStateConfiguration stateConfiguration : stateConfigurationList) {
                StateManagerResult.SubState subState = new StateManagerResult.SubState();
                Integer subStateType = stateConfiguration.getSubStateType();
                subState.setType(subStateType);
                subState.setDesc(stateConfiguration.getDescription());
                Boolean value = subStates.get(subStateType);
                subState.setValue(value != null ? value : true);
                subStateList.add(subState);
            }
        }

        result.setSubStateList(subStateList);

        return result;
    }


    @Override
    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public <T extends AbstractEntity, R extends StateManagerResult> Boolean changeState(StateManagerRequest<T> stateManagerRequest, OperationLogRequest operationLogRequest) {
        // 验证业务类型和状态类型
        String businessType = stateManagerRequest.getBusiness();
        Integer subStateType = stateManagerRequest.getSubStateType();
        StateManagerConfig.validBizAndType(businessType, subStateType);

        //查询变更前状态
        @SuppressWarnings("unchecked")
        R oldStateResult = (R) queryState(stateManagerRequest);

        // 获取实体类型对应的处理器
        String entityType = stateManagerRequest.getEntityType();
        StateManagerProcessor<AbstractEntity, StateManagerResult> processor = StateManagerProcessorRegistry.getProcessor(entityType);
        if (processor == null) {
            throw new IllegalStateException("未找到实体类型[" + entityType + "]对应的处理器");
        }

        T entity = stateManagerRequest.getEntity();

        // 初始化或获取状态实体
        AbstractStateDO abstractStateDO = initAndGetStateByBizIdAndEntity(stateManagerRequest.getBusiness(), stateManagerRequest.getEntity(), processor);
        String originalSubState = abstractStateDO.getSubStatesBits();
        // 更新子状态
        Map<Integer, Boolean> subStates = abstractStateDO.getSubStates();
        if (subStates == null) {
            subStates = new HashMap<>();
            abstractStateDO.setSubStates(subStates);
        }
        subStates.put(subStateType, stateManagerRequest.getEnabled());
        abstractStateDO.setSubStates(subStates);
        // 更新总状态和其他信息
        abstractStateDO.setRemark(stateManagerRequest.getRemark());
        abstractStateDO.setState(StateManagerConstant.ALL_ENABLED_STATE_BITS.equals(abstractStateDO.getSubStatesBits()));
        if (originalSubState != null && originalSubState.equals(abstractStateDO.getSubStatesBits())) {
            return Boolean.TRUE;
        }
        // 更新状态
        stateDao.update(abstractStateDO);

        //查询变更后状态
        @SuppressWarnings("unchecked")
        R curStateResult = (R) queryState(stateManagerRequest);


        // 后置处理
        processor.afterStateChange(entity, oldStateResult, curStateResult, operationLogRequest);

        //todo 记录日志

        return Boolean.TRUE;
    }


    private <T extends AbstractEntity, E extends AbstractStateDO> AbstractStateDO initAndGetStateByBizIdAndEntity(String business, T entity, StateManagerProcessor processor) {
        AbstractStateDO abstractStateDO;
        Criteria criteria = processor.generateCriteria(entity);
        if (criteria == null) {
            throw new StateManageBizException("generateCriteria return is null");
        }
        criteria.with(StateManagerConstant.BUSINESS).is(business);
        abstractStateDO = stateDao.filter(criteria).fetchOne();
        if (abstractStateDO == null) {
            try {
                E newEntity = (E) processor.buildStateDO(entity);
                newEntity.setBusiness(business);
                newEntity.setState(Boolean.TRUE);
                newEntity.setSubStates(null);
                stateDao.insert(newEntity);
            } catch (DuplicateKeyException e) {
                LOGGER.info("DuplicateKeyException");
            }
            abstractStateDO = stateDao.filter(criteria).fetchOne();
        }
        return abstractStateDO;
    }
}
