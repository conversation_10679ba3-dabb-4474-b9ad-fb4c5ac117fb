package com.wosai.pay.common.state.manager.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.pay.common.data.VersionedRecord;

public class BizDisableReasonDO extends VersionedRecord<Long> {
    /**
     * 业务类型
     */
    @JsonProperty("business" )
    private String business;

    /**
     * 状态类型
     */
    @JsonProperty("type" )
    private Integer type;

    /**
     * 子状态描述
     */
    @JsonProperty("description" )
    private String description;

    public String getBusiness() {
        return business;
    }

    public void setBusiness(String business) {
        this.business = business;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BizDisableReasonDO() {
    }

    public BizDisableReasonDO(String business, Integer type, String description) {
        this.business = business;
        this.type = type;
        this.description = description;
    }
}
