package com.wosai.pay.common.state.manager.request;

import lombok.Data;

@Data
public class CommonEntity extends AbstractEntity {

    public static final String OBJECT_TYPE_COMMON = "common";

    private String objectId;

    public CommonEntity() {
        setObjectType(objectType());
    }

    public static String objectType() {
        return OBJECT_TYPE_COMMON;
    }

    static {
        registerType( objectType(), CommonEntity.class);
    }

}
