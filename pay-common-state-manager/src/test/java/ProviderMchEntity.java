import com.wosai.pay.common.state.manager.request.AbstractEntity;
import lombok.Data;

@Data
public class ProviderMchEntity extends AbstractEntity {

    public static final String OBJECT_TYPE_PROVIDER_MCH = "provider_mch";
    
    private Integer provider;

    private String providerMchId;

    public ProviderMchEntity() {
        setObjectType(objectType());
    }
    public static String objectType() {
        return OBJECT_TYPE_PROVIDER_MCH;
    }

    static {
        registerType( objectType(), ProviderMchEntity.class);
    }
}
